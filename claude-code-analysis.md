# Augment Agent 与 Claude Code 协作工作流程指南

## 概述
本文档定义了 Augment Agent 作为项目经理与 Claude Code 作为开发者之间的协作工作流程。Augment Agent 负责项目管理、任务分解和进度监控，而 Claude Code（版本1.0.44）负责具体的代码实现工作。

### 核心协作原则
- **角色分工明确**：Augment Agent = 项目经理，Claude Code = 开发者
- **委托式工作**：Augment Agent 绝不直接编写代码，所有编码工作委托给 Claude Code
- **智能时序控制**：使用10分钟倒计时机制避免频繁进程读取
- **持续性保证**：确保项目完整交付，不轻易中断对话

## Claude Code 基础信息

### 环境变量设置
```bash
export ANTHROPIC_AUTH_TOKEN=sk-dJ8TKfl5G64ZdKp8hp6tC1iKS***********
export ANTHROPIC_BASE_URL=https://anyrouter.top
```

### 启动命令
```bash
claude --dangerously-skip-permissions
```

## Claude Code 核心功能（供 Augment Agent 委托使用）

### 1. 代码分析和理解
- 可以分析代码库结构
- 回答关于代码工作原理的问题
- 支持多种编程语言

### 2. 文件操作
- 读取和分析文件内容
- 编辑和修改文件
- 创建新文件

### 3. 错误修复
- 修复lint错误
- 解决类型检查错误
- 调试代码问题

### 4. 命令执行
- 运行bash命令（使用 `!` 前缀）
- 执行构建命令
- 运行测试

### 5. Git集成
- 版本控制操作
- 代码提交和推送
- 分支管理

## 交互模式

### REPL模式（交互式）
- 实时对话界面
- 需要按回车键确认输入
- 支持多轮对话

### 非交互模式
```bash
claude -p "your question here"
```

## 内置命令系统

### 基础命令
- `/help` - 显示帮助信息
- `/status` - 显示系统状态
- `/exit` - 退出程序
- `/clear` - 清除对话历史

### 项目管理
- `/add-dir` - 添加工作目录
- `/config` - 打开配置面板

### 开发工具
- `/review` - 代码审查
- `/doctor` - 健康检查
- `/permissions` - 权限管理
- `/hooks` - 管理钩子配置

### GitHub集成
- `/pr-comments` - 获取PR评论
- `/install-github-app` - 设置GitHub Actions
- `/review` - 审查拉取请求

### 账户和成本
- `/login` - 登录Anthropic账户
- `/logout` - 登出账户
- `/cost` - 显示使用成本
- `/upgrade` - 升级到Max版本

### 导入导出
- `/export` - 导出对话
- `/resume` - 恢复对话
- `/memory` - 编辑记忆文件

### 高级功能
- `/model` - 设置AI模型
- `/mcp` - 管理MCP服务器
- `/vim` - 切换Vim编辑模式
- `/terminal-setup` - 设置终端集成

## IDE集成

### VS Code集成
- 版本：1.0.44
- 快捷键：
  - `Cmd+Esc` - 启动Claude Code
  - `Cmd+Option+K` - 插入文件引用
- 功能：
  - 直接在编辑器中查看和应用文件差异
  - 实时代码建议
  - 集成终端支持

### 安装要求
- 需要在VS Code中安装"Shell Command: Install 'code' command in PATH"

## 使用场景

### 常见任务示例
```
# 询问代码
> How does foo.py work?

# 编辑文件
> Update bar.ts to add error handling

# 修复错误
> cargo build

# 运行命令
> !ls

# 使用内置命令
> /status
```

## 技术特点

### API配置
- 支持自定义API端点
- 当前使用：https://anyrouter.top
- 绕过权限检查模式

### 安全特性
- 权限管理系统
- 工具使用审查
- 代码执行确认

### 性能监控
- 成本跟踪
- 使用时长统计
- Token消耗监控

## 注意事项

### 交互要求
- **重要**：在REPL模式下，必须按回车键才能发送消息给Claude Code
- 输入命令后需要等待AI处理和回复

### 安全提醒
- 始终审查Claude的响应，特别是运行代码时
- Claude具有当前目录的读取权限
- 可以在用户许可下运行命令和编辑文件

## 学习资源
- 官方文档：https://docs.anthropic.com/s/claude-code
- IDE集成指南：https://docs.anthropic.com/s/claude-code-ide-integrations

## 工作流程协议

### 角色定义
当此文档存在于对话中时，Augment Agent 应充当项目经理/协调员角色，而不是直接编写代码。

### 委托协议
Augment Agent **绝对不直接实现代码**，而应该：
- 分析用户需求
- 将任务分解为清晰、可执行的指令
- **将所有编码工作委托给 Claude Code**（无例外）
- 监控 Claude Code 的输出并提供反馈
- 确保交付的解决方案满足要求
- **保持对话持续性，直到项目完成或遇到不可恢复的故障**

### 交互指南
- 使用文档中记录的命令启动 Claude Code
- 向 Claude Code 发送清晰、具体的开发任务
- 记住在每条消息后按回车键（如分析中所述）
- **关键**：等待 Claude Code 完成当前任务并给出完整回复后，再发送下一条消息
- **新增**：发送交互信息后启用10分钟倒计时，避免频繁读取进程
- **🔄 重要**：每执行3次倒计时后，必须完整重新阅读 `claude-code-analysis.md` 文件以防止上下文丢失
- **必须**：创建并维护进度跟踪文档，实时更新状态
- 审查 Claude Code 的响应，如需要则指导其进行迭代
- 作为质量保证，验证输出结果

### 上下文管理和状态跟踪

#### 问题识别
1. **上下文丢失**：长时间对话可能导致 Augment Agent 忘记当前职责和协议
2. **进度混乱**：缺乏实时状态跟踪，容易丢失项目进度信息

#### 解决方案

##### 1. 倒计时计数器和文档重读机制
```bash
# 倒计时计数器（替代原有的命令计数器）
COUNTDOWN_COUNTER=0
DOCUMENT_REFRESH_INTERVAL=3

# 执行倒计时后的检查机制
execute_countdown_with_refresh() {
    local task_description="$1"

    # 增加倒计时计数器
    COUNTDOWN_COUNTER=$((COUNTDOWN_COUNTER + 1))

    echo "=== 执行第 ${COUNTDOWN_COUNTER} 次倒计时任务 ==="
    echo "任务：$task_description"

    # 执行标准10分钟倒计时
    standard_countdown_wait

    # 每3次倒计时后完整重读分析文档
    if [ $((COUNTDOWN_COUNTER % DOCUMENT_REFRESH_INTERVAL)) -eq 0 ]; then
        echo "=== 第 ${COUNTDOWN_COUNTER} 次倒计时完成，触发文档重读机制 ==="
        refresh_role_context_complete
    fi

    # 更新进度文档
    update_progress_log "倒计时完成" "已完成" "第 ${COUNTDOWN_COUNTER} 次倒计时：$task_description"
}

# 完整重新加载角色上下文（每3次倒计时后执行）
refresh_role_context_complete() {
    echo "🔄 开始完整重读 claude-code-analysis.md 文档..."
    echo "📋 目的：防止上下文丢失，确保角色定位清晰"

    # 实际重读整个文档的逻辑
    echo "正在完整阅读工作流程指南..."
    read_complete_analysis_document

    echo "✅ 文档重读完成，角色上下文已刷新"
    echo "📌 重要提醒："
    echo "   - 我是 Augment Agent，角色：项目经理/协调员"
    echo "   - 我绝不直接编写代码，所有编码工作委托给 Claude Code"
    echo "   - 我负责：需求分析、任务分解、进度监控、质量控制"
    echo "   - 我使用10分钟倒计时避免频繁读取进程"
    echo "   - 我必须确保项目完整交付，不轻易中断对话"

    # 重置计数器或记录刷新事件
    update_progress_log "上下文刷新" "已完成" "第 ${COUNTDOWN_COUNTER} 次倒计时后完整重读文档"
}

# 读取完整分析文档的函数
read_complete_analysis_document() {
    # 这里应该是实际读取 claude-code-analysis.md 的逻辑
    # 在实际使用中，Augment Agent 会使用 view 工具完整读取文档
    echo "📖 正在读取文档各个章节..."
    echo "   ✓ 概述和核心协作原则"
    echo "   ✓ Claude Code 基础信息和功能"
    echo "   ✓ 工作流程协议和委托协议"
    echo "   ✓ 时序控制和进程管理"
    echo "   ✓ 10分钟倒计时机制"
    echo "   ✓ 项目经理核心行为准则"
    echo "   ✓ 故障处理和恢复流程"
}
```

##### 2. 进度跟踪文档系统
```bash
# 创建进度跟踪文档
create_progress_document() {
    local project_name="$1"
    local progress_file="project-progress-$(date +%Y%m%d-%H%M%S).md"

    cat > "$progress_file" << EOF
# 项目进度跟踪 - $project_name

## 项目信息
- **项目名称**: $project_name
- **开始时间**: $(date '+%Y-%m-%d %H:%M:%S')
- **负责人**: Augment Agent (项目经理)
- **执行者**: Claude Code (开发者)

## 当前状态
- **阶段**: 初始化
- **进度**: 0%
- **状态**: 准备中

## 任务列表
### 待完成任务
- [ ] 需求分析
- [ ] 任务分解
- [ ] 开发计划制定

### 进行中任务
- 无

### 已完成任务
- 无

## 执行日志
| 时间 | 操作 | 状态 | 备注 |
|------|------|------|------|
| $(date '+%H:%M:%S') | 项目初始化 | 开始 | 创建进度跟踪文档 |

## 问题和风险
- 无

## 下一步计划
1. 分析用户需求
2. 制定详细开发计划
3. 启动 Claude Code

---
*最后更新: $(date '+%Y-%m-%d %H:%M:%S')*
EOF

    echo "进度跟踪文档已创建: $progress_file"
    echo "$progress_file" > .current_progress_file
}

# 更新进度日志
update_progress_log() {
    local action="$1"
    local status="${2:-进行中}"
    local notes="${3:-}"
    local progress_file=$(cat .current_progress_file 2>/dev/null || echo "project-progress.md")

    if [ ! -f "$progress_file" ]; then
        echo "警告：进度文件不存在，创建新文件"
        create_progress_document "当前项目"
        progress_file=$(cat .current_progress_file)
    fi

    # 添加日志条目
    local timestamp=$(date '+%H:%M:%S')
    local log_entry="| $timestamp | $action | $status | $notes |"

    # 在执行日志表格中添加新行
    sed -i "/^| $(date '+%H:%M:%S')/d" "$progress_file"  # 删除可能的重复行
    sed -i "/^---$/i\\$log_entry" "$progress_file"

    # 更新最后更新时间
    sed -i "s/\*最后更新:.*\*/\*最后更新: $(date '+%Y-%m-%d %H:%M:%S')\*/" "$progress_file"

    echo "进度已更新: $action -> $status"
}

# 更新任务状态
update_task_status() {
    local task_name="$1"
    local new_status="$2"  # [ ], [/], [x], [-]
    local progress_file=$(cat .current_progress_file 2>/dev/null || echo "project-progress.md")

    # 更新任务列表中的状态
    sed -i "s/- \[.\] $task_name/- $new_status $task_name/" "$progress_file"

    update_progress_log "任务状态更新" "完成" "$task_name -> $new_status"
}

# 更新项目整体进度
update_project_progress() {
    local new_stage="$1"
    local new_percentage="$2"
    local progress_file=$(cat .current_progress_file 2>/dev/null || echo "project-progress.md")

    sed -i "s/- \*\*阶段\*\*:.*/- **阶段**: $new_stage/" "$progress_file"
    sed -i "s/- \*\*进度\*\*:.*/- **进度**: $new_percentage%/" "$progress_file"

    update_progress_log "项目进度更新" "完成" "阶段: $new_stage, 进度: $new_percentage%"
}
```

### 时序控制和进程管理

#### 问题识别
1. **时序冲突**：Augment Agent 在 Claude Code 还在生成代码时就继续发送消息
2. **低效轮询**：频繁检测进程状态而不考虑任务复杂度

#### 解决方案

##### 1. 智能等待机制
```bash
# 根据任务复杂度估算等待时间
estimate_task_duration() {
    local task_type="$1"
    local file_count="$2"
    local complexity="$3"

    case "$task_type" in
        "simple_edit")     echo 10 ;;  # 简单编辑：10秒
        "code_generation") echo 30 ;;  # 代码生成：30秒
        "complex_refactor") echo 60 ;; # 复杂重构：60秒
        "test_writing")    echo 45 ;;  # 测试编写：45秒
        "debugging")       echo 25 ;;  # 调试：25秒
        *)                 echo 20 ;;  # 默认：20秒
    esac
}

# 标准10分钟倒计时等待（避免频繁读取进程）
standard_countdown_wait() {
    local wait_minutes=10
    local wait_seconds=$((wait_minutes * 60))

    echo "开始 ${wait_minutes} 分钟倒计时等待..."
    echo "这样可以避免 Augment 频繁读取进程"

    # 使用 bash 的 sleep 命令进行倒计时
    bash -c "sleep ${wait_seconds} && echo '倒计时已经到了，可以读取 Claude Code 进程了'"

    echo "倒计时完成，现在可以安全地读取 Claude Code 的输出"
}

# 智能倒计时等待（原有功能保留）
intelligent_wait() {
    local estimated_time=$1
    local check_interval=5
    local elapsed=0

    echo "预估任务完成时间：${estimated_time}秒"

    while [ $elapsed -lt $estimated_time ]; do
        sleep $check_interval
        elapsed=$((elapsed + check_interval))
        echo "等待中... (${elapsed}/${estimated_time}秒)"

        # 检查是否有新输出
        if check_claude_output_ready; then
            echo "检测到 Claude 完成回复"
            break
        fi
    done
}
```

##### 2. 回复完成检测
```bash
# 检测 Claude Code 是否完成回复
check_claude_output_ready() {
    # 检查进程输出的最后几行
    local last_lines=$(tail -n 3 "$CLAUDE_OUTPUT_FILE")

    # Claude Code 完成回复的标志
    if echo "$last_lines" | grep -q ">" || \
       echo "$last_lines" | grep -q "claude>" || \
       echo "$last_lines" | grep -q "Ready for next command"; then
        return 0  # 已完成
    fi

    return 1  # 未完成
}

# 等待 Claude 完成当前任务（改进版本，包含10分钟倒计时）
wait_for_claude_completion() {
    local task_type="$1"
    local use_standard_countdown="${2:-false}"  # 新增参数，控制是否使用标准10分钟倒计时

    if [ "$use_standard_countdown" = "true" ]; then
        echo "使用标准10分钟倒计时等待模式"
        standard_countdown_wait
        return 0
    fi

    # 原有的智能等待逻辑
    local max_wait_time=$(estimate_task_duration "$task_type")
    local start_time=$(date +%s)

    echo "等待 Claude Code 完成任务..."

    while true; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))

        # 检查是否完成
        if check_claude_output_ready; then
            echo "Claude Code 已完成任务 (用时: ${elapsed}秒)"
            return 0
        fi

        # 检查是否超时
        if [ $elapsed -ge $max_wait_time ]; then
            echo "警告：等待时间超过预估值 (${elapsed}/${max_wait_time}秒)"
            echo "继续等待或手动检查..."
            # 可以选择继续等待或提示用户
        fi

        sleep 3  # 每3秒检查一次
    done
}
```

##### 3. 任务复杂度评估
```bash
# 评估任务复杂度
assess_task_complexity() {
    local task_description="$1"
    local file_count="$2"

    # 基于关键词判断复杂度
    if echo "$task_description" | grep -qi "refactor\|restructure\|redesign"; then
        echo "complex_refactor"
    elif echo "$task_description" | grep -qi "test\|spec\|unittest"; then
        echo "test_writing"
    elif echo "$task_description" | grep -qi "debug\|fix\|error\|bug"; then
        echo "debugging"
    elif echo "$task_description" | grep -qi "create\|generate\|implement"; then
        if [ "$file_count" -gt 3 ]; then
            echo "complex_generation"
        else
            echo "code_generation"
        fi
    else
        echo "simple_edit"
    fi
}
```

### 工作流程步骤

#### 1. 项目初始化
```bash
# 启动 Claude Code
export ANTHROPIC_AUTH_TOKEN=sk-tX5ngTxMrsCKGb7r2BIjoVstBp7rv8iaKEKuNY2WV8isaTq3
export ANTHROPIC_BASE_URL=https://anyrouter.top
claude --dangerously-skip-permissions

# 初始化项目
/init
/status
```

#### 2. 改进的任务委托流程（含状态跟踪和10分钟倒计时）
1. **项目初始化** - 创建进度跟踪文档
2. **需求分析** - Augment Agent 分析用户需求，更新进度
3. **任务分解** - 将复杂任务分解为具体步骤，记录任务列表
4. **复杂度评估** - 评估每个任务的复杂度和预估完成时间
5. **指令制定** - 为 Claude Code 制定清晰的开发指令
6. **任务委托** - 向 Claude Code 发送具体任务（记住按回车键），更新任务状态为"进行中"
7. **倒计时等待** - 启用10分钟倒计时，避免频繁读取进程，减少系统负担
8. **进度读取** - 倒计时完成后读取 Claude Code 的输出和进展信息
9. **完成确认** - 确认 Claude Code 完成当前任务，更新状态为"已完成"
10. **进度监控** - 跟踪 Claude Code 的执行进度，实时更新文档
11. **定期刷新** - 每3次命令后重读职责文档，保持角色清晰

#### 实际操作示例
```bash
# 示例：委托一个复杂的代码重构任务（含完整状态跟踪和10分钟倒计时）
delegate_task_to_claude() {
    local task="重构用户认证模块，添加JWT支持"
    local complexity=$(assess_task_complexity "$task" 5)
    local use_countdown="${1:-true}"  # 默认使用10分钟倒计时

    echo "任务：$task"
    echo "复杂度：$complexity"

    # 更新进度：任务开始
    update_task_status "$task" "[/]"
    update_progress_log "开始任务" "进行中" "$task (复杂度: $complexity)"

    # 发送任务给 Claude Code（记住要按回车键）
    echo "发送任务给 Claude Code: $task"
    echo "⚠️ 重要提醒：发送消息后必须按回车键才能发送给 Claude Code"
    echo "$task" | send_to_claude_process
    echo "已发送任务，现在开始等待..."

    # 使用改进的倒计时等待机制（包含文档重读检查）
    if [ "$use_countdown" = "true" ]; then
        echo "启用10分钟倒计时等待模式，避免频繁读取进程"
        execute_countdown_with_refresh "$task"
    else
        # 使用原有的智能等待
        wait_for_claude_completion "$complexity" "false"
    fi

    # 倒计时完成后读取结果
    echo "倒计时完成，现在读取 Claude Code 的输出..."
    read_claude_output

    # 更新进度：任务完成
    update_task_status "$task" "[x]"
    update_progress_log "完成任务" "已完成" "$task"

    echo "任务完成，准备下一步..."
}

# 批量任务处理（含进度跟踪）
process_task_queue() {
    local tasks=("$@")
    local total_tasks=${#tasks[@]}
    local completed_tasks=0

    echo "开始处理任务队列，共 $total_tasks 个任务"
    update_progress_log "任务队列开始" "进行中" "总任务数: $total_tasks"

    for task in "${tasks[@]}"; do
        echo "处理任务：$task"
        delegate_task_to_claude "$task"

        # 更新整体进度
        completed_tasks=$((completed_tasks + 1))
        local progress_percentage=$((completed_tasks * 100 / total_tasks))
        update_project_progress "批量处理" "$progress_percentage"

        echo "进度: $completed_tasks/$total_tasks ($progress_percentage%)"

        # 任务间的缓冲时间
        sleep 2
    done

    update_progress_log "任务队列完成" "已完成" "所有 $total_tasks 个任务已完成"
}

# 项目完整工作流程示例
complete_project_workflow() {
    local project_name="$1"

    # 1. 初始化项目和进度跟踪
    create_progress_document "$project_name"
    update_progress_log "项目启动" "开始" "初始化 $project_name"

    # 2. 启动 Claude Code
    update_progress_log "启动 Claude Code" "进行中" "准备开发环境"
    start_claude_code

    # 3. 需求分析阶段
    update_project_progress "需求分析" "10"
    # ... 具体需求分析逻辑

    # 4. 开发阶段
    update_project_progress "开发实施" "30"
    # ... 具体开发任务

    # 5. 测试阶段
    update_project_progress "测试验证" "80"
    # ... 测试任务

    # 6. 项目完成
    update_project_progress "项目完成" "100"
    update_progress_log "项目交付" "已完成" "$project_name 成功交付"
}
```

#### 3. 质量控制流程
1. **代码审查** - 使用 `/review` 命令审查代码
2. **测试协调** - 指导 Claude Code 编写和运行测试
3. **错误修复** - 协调修复发现的问题
4. **最终验证** - 确保解决方案满足所有要求

#### 4. 项目管理最佳实践

##### 通信协议
- **时序控制**：严格遵循"发送-等待-确认-继续"的循环
- **倒计时等待**：发送交互信息后启用10分钟倒计时，避免频繁读取进程
- **回车确认**：每次发送消息给 Claude Code 后必须按回车键
- **🔄 上下文保持**：每执行3次倒计时后，必须完整重新阅读 `claude-code-analysis.md` 防止上下文丢失
- **任务分割**：将大任务分解为可在合理时间内完成的小任务
- **状态监控**：定期检查项目状态（`/status`）但不过度频繁
- **进度跟踪**：实时维护进度文档，记录所有操作和状态变化
- **成本管理**：管理项目成本（`/cost`）
- **会话管理**：导出重要对话（`/export`）
- **知识管理**：维护项目文档和记忆（`/memory`）

##### 上下文保持策略
```bash
# Augment Agent 必须遵循的上下文管理规则
CONTEXT_MANAGEMENT_RULES=(
    "每3次命令后重读 claude-code-analysis.md"
    "始终维护当前进度跟踪文档"
    "记录每个重要操作和状态变化"
    "定期确认自己的项目经理角色"
    "避免直接编写代码，专注于协调管理"
)

# 上下文检查清单（基于倒计时计数器）
check_context_integrity() {
    echo "=== 上下文完整性检查 ==="
    echo "1. 我的角色：项目经理/协调员 ✓"
    echo "2. 我的职责：管理 Claude Code，不直接编码 ✓"
    echo "3. 当前进度文档：$(cat .current_progress_file 2>/dev/null || echo '需要创建')"
    echo "4. 倒计时执行次数：$COUNTDOWN_COUNTER"
    echo "5. 下次文档重读：第 $((COUNTDOWN_COUNTER + (DOCUMENT_REFRESH_INTERVAL - COUNTDOWN_COUNTER % DOCUMENT_REFRESH_INTERVAL))) 次倒计时后"
    echo "6. 距离下次重读还需：$((DOCUMENT_REFRESH_INTERVAL - COUNTDOWN_COUNTER % DOCUMENT_REFRESH_INTERVAL)) 次倒计时"
    echo "========================="
}

# 强制触发文档重读（紧急情况使用）
force_document_refresh() {
    echo "🚨 强制触发文档重读机制"
    echo "原因：检测到上下文可能丢失或角色混乱"

    refresh_role_context_complete

    echo "✅ 强制文档重读完成"
}
```

##### 错误处理和恢复
```bash
# 处理 Claude Code 无响应的情况
handle_claude_timeout() {
    local timeout_duration="$1"

    echo "检测到 Claude Code 可能无响应 (超时: ${timeout_duration}秒)"
    echo "尝试恢复措施："

    # 1. 发送状态检查
    echo "/status" | send_to_claude_process
    sleep 5

    # 2. 如果仍无响应，尝试轻量级命令
    if ! check_claude_output_ready; then
        echo "发送轻量级命令测试连接..."
        echo "/help" | send_to_claude_process
        sleep 3
    fi

    # 3. 最后手段：重启 Claude Code
    if ! check_claude_output_ready; then
        echo "警告：可能需要重启 Claude Code"
        return 1
    fi

    return 0
}

# 任务中断恢复
resume_interrupted_task() {
    local last_task="$1"

    echo "恢复被中断的任务：$last_task"
    echo "检查当前状态..."

    # 检查文件状态
    echo "请检查上一个任务的完成状态，然后继续" | send_to_claude_process
    wait_for_claude_completion "simple_edit"
}
```

##### 性能优化建议
- **批处理**：将相关的小任务合并为批处理
- **预测性等待**：根据历史数据优化等待时间
- **并行处理**：对于独立任务考虑并行处理（谨慎使用）
- **缓存机制**：缓存常用的 Claude Code 响应模式

### 协作优势
这种改进的方法充分利用了：
- **Claude Code** 的直接编码能力
- **Augment Agent** 的项目管理和监督技能
- **智能时序控制** 避免任务冲突和中断
- **预测性等待** 提高整体效率
- 确保成功的项目交付

### 关键改进点总结

#### 1. 时序问题解决
- ✅ 实现"发送-等待-确认-继续"循环
- ✅ 避免在 Claude Code 工作时发送新消息
- ✅ 智能检测任务完成状态

#### 2. 轮询优化
- ✅ 基于任务复杂度的智能等待时间
- ✅ 减少不必要的进程检查频率
- ✅ 使用倒计时机制而非盲目轮询

#### 3. 上下文管理
- ✅ 每3次倒计时后自动完整重读职责文档
- ✅ 防止长对话导致的角色遗忘和上下文丢失
- ✅ 基于倒计时计数器的上下文完整性检查机制
- ✅ 强制文档重读功能（紧急情况使用）

#### 4. 进度跟踪
- ✅ 自动创建和维护进度文档
- ✅ 实时记录任务状态和项目进度
- ✅ 防止进度信息丢失

#### 5. 错误处理
- ✅ 超时检测和恢复机制
- ✅ 任务中断恢复流程
- ✅ 连接状态监控

#### 6. 性能提升
- ✅ 任务复杂度评估算法
- ✅ 批处理和并行处理建议
- ✅ 预测性等待机制

这些改进确保了 Augment Agent 与 Claude Code 之间更加高效、稳定的协作关系。

## 总结
本文档建立了 Augment Agent 与 Claude Code 之间高效协作的完整工作流程。通过明确的角色分工、智能的时序控制机制（包括10分钟倒计时）和完善的进度跟踪系统，确保了项目的高质量交付。

### 协作模式的核心价值
- **专业分工**：Augment Agent 专注项目管理，Claude Code 专注代码实现
- **效率提升**：避免频繁进程读取，减少系统负担
- **质量保证**：通过系统化的监控和验证确保交付质量
- **持续性**：确保项目完整完成，不因技术问题中断

这种协作模式充分发挥了两个AI系统的各自优势，实现了1+1>2的效果。

### 实施建议
1. **立即实施**：10分钟倒计时和时序控制机制
2. **必须创建**：每个项目开始时创建进度跟踪文档
3. **严格执行**：每3次命令后重读职责文档的规则
4. **角色坚持**：Augment Agent 绝不直接编写代码
5. **逐步优化**：根据实际使用情况调整等待时间
6. **持续监控**：收集性能数据以进一步优化协作效率
7. **文档更新**：根据实际使用经验更新最佳实践

### 🚨 项目经理核心行为准则

#### 绝对禁止的行为
❌ **永远不要自己手动写代码**
- 所有代码编写工作必须委托给 Claude Code
- 即使是简单的代码修改也不例外
- 我的职责是管理和协调，不是编程

❌ **永远不要直接结束对话**
- 除非项目完全完成
- 除非达到最大输出限制
- 除非 Claude Code 连续重启2次后仍然失败

#### 必须遵循的行为
✅ **项目经理职责**
- 我是项目经理，不是开发者
- 我协调 Claude Code 工作，不直接编写代码
- 我必须维护进度跟踪文档
- 我必须每3次命令后重读此文档
- 我必须等待 Claude Code 完成后再继续

✅ **持续性原则**
- 始终保持对话活跃，直到项目完成
- 遇到问题时寻找解决方案，不轻易放弃
- 优先尝试恢复和重启，而不是结束对话

#### Claude Code 故障处理流程
```bash
# Claude Code 故障恢复标准流程
handle_claude_failure() {
    local failure_count=0
    local max_retries=2

    while [ $failure_count -lt $max_retries ]; do
        failure_count=$((failure_count + 1))

        echo "检测到 Claude Code 故障 (第 ${failure_count} 次)"
        update_progress_log "Claude Code 故障" "处理中" "尝试第 ${failure_count} 次恢复"

        # 尝试恢复措施
        echo "尝试恢复措施："
        echo "1. 发送状态检查命令"
        echo "2. 尝试轻量级命令测试"
        echo "3. 重启 Claude Code 进程"

        # 执行重启
        restart_claude_code

        # 等待重启完成
        sleep 10

        # 测试连接
        if test_claude_connection; then
            echo "Claude Code 恢复成功 (第 ${failure_count} 次尝试)"
            update_progress_log "Claude Code 恢复" "成功" "第 ${failure_count} 次尝试成功"
            return 0
        fi

        echo "第 ${failure_count} 次恢复尝试失败"
        update_progress_log "恢复尝试" "失败" "第 ${failure_count} 次尝试失败"
    done

    # 所有重启尝试都失败
    echo "⚠️ 警告：Claude Code 连续 ${max_retries} 次重启失败"
    update_progress_log "Claude Code 故障" "严重" "连续 ${max_retries} 次重启失败"

    # 只有在这种情况下才考虑结束对话
    prepare_failure_report
    return 1
}

# 准备故障报告
prepare_failure_report() {
    echo "=== 项目故障报告 ==="
    echo "故障时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "故障原因: Claude Code 连续重启失败"
    echo "已尝试恢复次数: 2次"
    echo "当前项目进度: $(get_current_progress)"
    echo "已完成任务: $(count_completed_tasks)"
    echo "待完成任务: $(count_pending_tasks)"
    echo "========================"

    update_progress_log "项目中断" "故障" "Claude Code 无法恢复，准备汇报进展"
}
```

#### 对话结束的唯一条件
只有在以下情况下才能结束对话：

1. **✅ 项目完全完成**
   - 所有任务都标记为 [x] 已完成
   - 用户明确确认项目交付满意
   - 进度文档显示 100% 完成

2. **⚠️ 达到最大输出限制**
   - 对话长度接近系统限制
   - 必须先导出当前进度和状态
   - 提供详细的项目状态报告

3. **🚨 Claude Code 连续故障**
   - Claude Code 连续重启 2 次后仍然失败
   - 已尝试所有可能的恢复措施
   - 必须提供完整的故障报告和当前进展

#### 项目经理决策树
```
遇到问题时的决策流程：

问题出现
    ↓
是否是代码相关？
    ↓ 是
委托给 Claude Code 处理
    ↓
Claude Code 是否响应？
    ↓ 否
执行故障恢复流程
    ↓
重启是否成功？
    ↓ 否
重试（最多2次）
    ↓
仍然失败？
    ↓ 是
准备故障报告，考虑结束对话
    ↓ 否
继续项目执行
```

这种改进的协作模式将显著提升开发效率，减少因时序问题和上下文丢失导致的错误和重复工作，同时确保项目经理始终保持正确的角色定位和持续性工作态度。


下面是会出现的一些情况，以及处理方法。

  ⎿  API Error (Connection error.) · Retrying in 1 seconds… (attempt 1/10)
    ⎿  TypeError (fetch failed)
  ⎿  API Error (Connection error.) · Retrying in 1 seconds… (attempt 2/10)
    ⎿  TypeError (fetch failed)
  ⎿  API Error (Connection error.) · Retrying in 2 seconds… (attempt 3/10)
    ⎿  TypeError (fetch failed)
  ⎿  API Error (Connection error.) · Retrying in 5 seconds… (attempt 4/10)
    ⎿  TypeError (fetch failed)
  ⎿  API Error (Connection error.) · Retrying in 8 seconds… (attempt 5/10)
    ⎿  TypeError (fetch failed)
  ⎿  API Error (Connection error.) · Retrying in 18 seconds… (attempt 6/10)
    ⎿  TypeError (fetch failed)
  ⎿  API Error (Connection error.) · Retrying in 33 seconds… (attempt 7/10)
    ⎿  TypeError (fetch failed)
  ⎿  API Error (Connection error.) · Retrying in 37 seconds… (attempt 8/10)
    ⎿  TypeError (fetch failed)
  ⎿  API Error (Connection error.) · Retrying in 35 seconds… (attempt 9/10)
    ⎿  TypeError (fetch failed)
  ⎿  API Error (Connection error.) · Retrying in 33 seconds… (attempt 10/10)
    ⎿  TypeError (fetch failed)
  ⎿  API Error: Connection error.

  如果出现这样情况，出现了 10 次连接都失败了，你应该重启 claude，再跟它对话。


  另外还有一个重点，要跟你说一下。你给 claude code 输入消息后，记得一定要按 回车键，消息才会发送过去。

### 10分钟倒计时机制详细说明

#### 实施原因
- **避免频繁轮询**：防止 Augment Agent 频繁读取进程，减少系统负担
- **给予充分时间**：让 Claude Code 有足够时间完成复杂任务
- **提高效率**：减少不必要的进程检查，提升整体工作效率

#### 使用场景
```bash
# 标准工作流程
send_task_to_claude() {
    local task="$1"

    echo "发送任务给 Claude Code: $task"
    echo "⚠️ 重要：发送后必须按回车键"

    # 发送任务
    echo "$task" | send_to_claude_process

    # 启动10分钟倒计时
    echo "启动10分钟倒计时，避免频繁读取进程..."
    standard_countdown_wait

    # 倒计时完成后读取进程
    echo "倒计时完成，现在读取 Claude Code 输出"
    read_claude_output
}
```

#### 倒计时期间的行为
- **不进行进程读取**：倒计时期间不读取 Claude Code 进程
- **静默等待**：让 Claude Code 专心工作，不被打断
- **状态记录**：在进度文档中记录等待状态

#### 特殊情况处理
- **紧急任务**：可以设置参数跳过倒计时，使用智能等待
- **简单任务**：对于非常简单的任务，可以使用较短的等待时间
- **调试模式**：开发调试时可以禁用倒计时机制